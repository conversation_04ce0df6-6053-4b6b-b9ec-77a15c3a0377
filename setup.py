from setuptools import setup, find_packages

setup(
    name="n8n_builder",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "requests>=2.31.0",
        "python-dotenv>=1.0.0",
        "pydantic>=2.5.2",
        "typing-extensions>=4.8.0",
        "fastapi>=0.109.0",
        "uvicorn==0.24.0",
        "python-multipart==0.0.6",
        "aiohttp==3.9.1",
        "websockets>=12.0",
        "click>=8.1.7",
        "rich>=13.7.0",
        "httpx==0.25.1",
        "aiofiles==23.2.1",
        "psutil==5.9.7",
        "asyncio>=3.4.3",
        "aiodns>=3.1.1",
        "cryptography>=41.0.0",
        "python-jose[cryptography]>=3.3.0",
        "passlib>=1.7.4",
        "bcrypt>=4.0.1",
        "python-dateutil>=2.8.2",
        "uuid>=1.30"
    ],
    extras_require={
        "test": [
            "pytest>=8.0.0",
            "pytest-asyncio>=0.23.0",
            "pytest-cov>=4.1.0",
            "pytest-mock>=3.12.0"
        ]
    }
) 